@page "/orders"
@page "/orders/{OrderId:int}"
@using NafaPlace.SellerPortal.Models.Orders
@using NafaPlace.SellerPortal.Models.Statistics
@using NafaPlace.SellerPortal.Services
@inject IOrderService OrderService
@inject IJSRuntime JSRuntime
@inject IAuthService AuthService
@inject ProductService ProductService

<h1 class="visually-hidden">Gestion des Commandes - NafaPlace</h1>

<div class="container-fluid px-4">
    <h1 class="mt-4">Gestion des Commandes</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="/dashboard">Dashboard</a></li>
        <li class="breadcrumb-item active">Commandes</li>
    </ol>

    @if (OrderId.HasValue && OrderId.Value > 0)
    {
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-file-invoice me-1"></i>
                    Détails de la Commande #@_currentOrder.OrderNumber
                </div>
                <button class="btn btn-outline-secondary" @onclick="BackToOrdersList">
                    <i class="fas fa-arrow-left me-1"></i> Retour à la liste
                </button>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>Informations Client</h5>
                        <div class="mb-2"><strong>Nom:</strong> @_currentOrder.CustomerName</div>
                        <div class="mb-2"><strong>Email:</strong> @_currentOrder.CustomerEmail</div>
                        <div class="mb-2"><strong>Téléphone:</strong> @_currentOrder.CustomerPhone</div>
                        <div class="mb-2"><strong>Date de commande:</strong> @_currentOrder.OrderDate.ToString("dd/MM/yyyy HH:mm")</div>
                    </div>
                    <div class="col-md-6">
                        <h5>Informations Livraison</h5>
                        <div class="mb-2"><strong>Adresse:</strong> @_currentOrder.ShippingAddress</div>
                        <div class="mb-2"><strong>Ville:</strong> @_currentOrder.ShippingCity</div>
                        <div class="mb-2"><strong>Méthode de livraison:</strong> @_currentOrder.ShippingMethod</div>
                        <div class="mb-2">
                            <strong>Statut:</strong>
                            <select class="form-select form-select-sm d-inline-block w-auto ms-2" @bind="_currentOrder.Status">
                                <option value="En attente">En attente</option>
                                <option value="Confirmé">Confirmé</option>
                                <option value="En préparation">En préparation</option>
                                <option value="Expédié">Expédié</option>
                                <option value="Livré">Livré</option>
                                <option value="Annulé">Annulé</option>
                            </select>
                            <button class="btn btn-sm btn-primary ms-2" @onclick="UpdateOrderStatus">Mettre à jour</button>
                        </div>
                    </div>
                </div>

                <h5>Produits Commandés</h5>
                <table class="table table-bordered table-striped">
                    <thead>
                        <tr>
                            <th style="width: 80px;">Image</th>
                            <th>Produit</th>
                            <th>Prix unitaire</th>
                            <th>Quantité</th>
                            <th>Total</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var item in _currentOrder.Items)
                        {
                            <tr>
                                <td>
                                    <img src="@item.ProductImageUrl" alt="@item.ProductName" class="img-thumbnail" style="width: 60px; height: 60px; object-fit: cover;" />
                                </td>
                                <td>@item.ProductName</td>
                                <td>@item.UnitPrice GNF</td>
                                <td>@item.Quantity</td>
                                <td>@(item.UnitPrice * item.Quantity) GNF</td>
                            </tr>
                        }
                    </tbody>
                    <tfoot>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Sous-total:</strong></td>
                            <td>@_currentOrder.Subtotal GNF</td>
                        </tr>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Frais de livraison:</strong></td>
                            <td>@_currentOrder.ShippingFee GNF</td>
                        </tr>
                        <tr>
                            <td colspan="4" class="text-end"><strong>Total:</strong></td>
                            <td><strong>@_currentOrder.TotalAmount GNF</strong></td>
                        </tr>
                    </tfoot>
                </table>

                <div class="row mt-4">
                    <div class="col-md-6">
                        <h5>Paiement</h5>
                        <div class="mb-2"><strong>Méthode:</strong> @_currentOrder.PaymentMethod</div>
                        <div class="mb-2"><strong>Statut:</strong> @_currentOrder.PaymentStatus</div>
                        @if (_currentOrder.PaymentStatus != "Payé")
                        {
                            <button class="btn btn-success" @onclick="MarkAsPaid">Marquer comme payé</button>
                        }
                    </div>
                    <div class="col-md-6">
                        <h5>Notes</h5>
                        <textarea class="form-control" rows="3" @bind="_currentOrder.Notes"></textarea>
                        <button class="btn btn-primary mt-2" @onclick="SaveNotes">Enregistrer les notes</button>
                    </div>
                </div>
            </div>
        </div>
    }
    else
    {
        <div class="card mb-4">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div>
                    <i class="fas fa-table me-1"></i>
                    Liste des Commandes
                </div>
                <div>
                    <button class="btn btn-outline-primary me-2" @onclick="ExportOrders">
                        <i class="fas fa-file-export me-1"></i> Exporter
                    </button>
                    <button class="btn btn-outline-secondary" @onclick="RefreshOrders">
                        <i class="fas fa-sync me-1"></i> Actualiser
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-4">
                        <div class="input-group">
                            <input type="text" class="form-control" placeholder="Rechercher..." @bind="_searchTerm" @bind:event="oninput">
                            <button class="btn btn-outline-secondary" type="button" @onclick="SearchOrders">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" @bind="_selectedStatus">
                            <option value="">Tous les statuts</option>
                            <option value="En attente">En attente</option>
                            <option value="Confirmé">Confirmé</option>
                            <option value="En préparation">En préparation</option>
                            <option value="Expédié">Expédié</option>
                            <option value="Livré">Livré</option>
                            <option value="Annulé">Annulé</option>
                        </select>
                    </div>
                    <div class="col-md-5">
                        <div class="input-group">
                            <span class="input-group-text">Du</span>
                            <input type="date" class="form-control" @bind="_startDate">
                            <span class="input-group-text">Au</span>
                            <input type="date" class="form-control" @bind="_endDate">
                            <button class="btn btn-outline-secondary" type="button" @onclick="FilterByDate">
                                <i class="fas fa-filter"></i>
                            </button>
                        </div>
                    </div>
                </div>

                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Total</th>
                            <th>Statut</th>
                            <th>Paiement</th>
                            <th style="width: 120px;">Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (_ordersResponse?.Orders != null)
                        {
                            @foreach (var order in _ordersResponse.Orders)
                            {
                                <tr>
                                    <td>@order.OrderNumber</td>
                                    <td>@order.CustomerName</td>
                                    <td>@order.OrderDate.ToString("dd/MM/yyyy")</td>
                                    <td>@order.TotalAmount.ToString("N0") GNF</td>
                                    <td>
                                        <span class="badge @GetStatusBadgeClass(order.Status)">
                                            @order.Status
                                        </span>
                                    </td>
                                    <td>
                                        <span class="badge @GetPaymentStatusBadgeClass(order.PaymentStatus)">
                                            @order.PaymentStatus
                                        </span>
                                    </td>
                                    <td>
                                        <a href="/orders/@order.Id" class="btn btn-sm btn-primary me-1">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <button class="btn btn-sm btn-danger" @onclick="() => CancelOrder(order)"
                                                disabled="@(order.Status == "Livré" || order.Status == "Annulé")">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </td>
                                </tr>
                            }
                        }
                    </tbody>
                </table>

                @if (_ordersResponse != null && _ordersResponse.TotalPages > 1)
                {
                    <nav aria-label="Page navigation">
                        <ul class="pagination justify-content-center">
                            <li class="page-item @(!_ordersResponse.HasPreviousPage ? "disabled" : "")">
                                <a class="page-link" href="javascript:void(0)" @onclick="PreviousPage">Précédent</a>
                            </li>
                            @for (int i = 1; i <= _ordersResponse.TotalPages; i++)
                            {
                                var pageNumber = i;
                                <li class="page-item @(pageNumber == _ordersResponse.PageNumber ? "active" : "")">
                                    <a class="page-link" href="javascript:void(0)" @onclick="() => GoToPage(pageNumber)">@pageNumber</a>
                                </li>
                            }
                            <li class="page-item @(!_ordersResponse.HasNextPage ? "disabled" : "")">
                                <a class="page-link" href="javascript:void(0)" @onclick="NextPage">Suivant</a>
                            </li>
                        </ul>
                    </nav>
                }
            </div>
        </div>
    }
</div>

@code {
    [Parameter]
    public int? OrderId { get; set; }

    private OrdersPagedResponse? _ordersResponse;
    private Order _currentOrder = new Order();
    private string _searchTerm = "";
    private string _selectedStatus = "";
    private DateTime _startDate = DateTime.Now.AddDays(-30);
    private DateTime _endDate = DateTime.Now;
    private bool _isLoading = false;
    private int _sellerId = 0; // ID du vendeur connecté

    protected override async Task OnInitializedAsync()
    {
        // Récupérer l'ID du vendeur connecté
        await LoadSellerInfo();

        if (_sellerId > 0)
        {
            await LoadOrdersAsync();
        }
    }

    protected override async Task OnParametersSetAsync()
    {
        if (OrderId.HasValue && OrderId.Value > 0)
        {
            await LoadOrderDetailsAsync(OrderId.Value);
        }
    }

    private async Task LoadOrdersAsync()
    {
        try
        {
            _isLoading = true;
            StateHasChanged();

            var request = new OrderFilterRequest
            {
                SearchTerm = string.IsNullOrEmpty(_searchTerm) ? null : _searchTerm,
                Status = string.IsNullOrEmpty(_selectedStatus) ? null : _selectedStatus,
                StartDate = _startDate,
                EndDate = _endDate,
                PageNumber = _ordersResponse?.PageNumber ?? 1,
                PageSize = 10,
                SellerId = _sellerId // Filtrer par vendeur
            };

            _ordersResponse = await OrderService.GetOrdersAsync(request);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading orders: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
            StateHasChanged();
        }
    }

    private async Task LoadOrderDetailsAsync(int orderId)
    {
        try
        {
            var order = await OrderService.GetOrderByIdAsync(orderId);
            if (order != null)
            {
                _currentOrder = order;
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error loading order details: {ex.Message}");
        }
    }

    private async Task SearchOrders()
    {
        if (_ordersResponse != null)
        {
            _ordersResponse.PageNumber = 1;
        }
        await LoadOrdersAsync();
    }

    private async Task FilterByDate()
    {
        if (_ordersResponse != null)
        {
            _ordersResponse.PageNumber = 1;
        }
        await LoadOrdersAsync();
    }

    private async Task PreviousPage()
    {
        if (_ordersResponse != null && _ordersResponse.HasPreviousPage)
        {
            _ordersResponse.PageNumber--;
            await LoadOrdersAsync();
        }
    }

    private async Task NextPage()
    {
        if (_ordersResponse != null && _ordersResponse.HasNextPage)
        {
            _ordersResponse.PageNumber++;
            await LoadOrdersAsync();
        }
    }

    private async Task GoToPage(int page)
    {
        if (_ordersResponse != null && page >= 1 && page <= _ordersResponse.TotalPages)
        {
            _ordersResponse.PageNumber = page;
            await LoadOrdersAsync();
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "En attente" => "bg-warning",
            "Confirmé" => "bg-info",
            "En préparation" => "bg-primary",
            "Expédié" => "bg-info",
            "Livré" => "bg-success",
            "Annulé" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private string GetPaymentStatusBadgeClass(string status)
    {
        return status switch
        {
            "Payé" => "bg-success",
            "En attente" => "bg-warning",
            "Remboursé" => "bg-info",
            "Échoué" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    private async Task CancelOrder(Order order)
    {
        try
        {
            var request = new UpdateOrderStatusRequest
            {
                OrderId = order.Id,
                Status = "Annulé"
            };

            var success = await OrderService.UpdateOrderStatusAsync(request);
            if (success)
            {
                await LoadOrdersAsync();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error cancelling order: {ex.Message}");
        }
    }

    private async Task RefreshOrders()
    {
        await LoadOrdersAsync();
    }

    private async Task ExportOrders()
    {
        try
        {
            var request = new OrderFilterRequest
            {
                StartDate = _startDate,
                EndDate = _endDate,
                Status = string.IsNullOrEmpty(_selectedStatus) ? null : _selectedStatus,
                SearchTerm = string.IsNullOrEmpty(_searchTerm) ? null : _searchTerm
            };

            var fileData = await OrderService.ExportOrdersAsync(request, "excel");
            if (fileData != null && fileData.Length > 0)
            {
                await JSRuntime.InvokeVoidAsync("downloadFile", "commandes.xlsx", Convert.ToBase64String(fileData));
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error exporting orders: {ex.Message}");
        }
    }

    private void BackToOrdersList()
    {
        OrderId = null;
    }

    private async Task UpdateOrderStatus()
    {
        try
        {
            var request = new UpdateOrderStatusRequest
            {
                OrderId = _currentOrder.Id,
                Status = _currentOrder.Status
            };

            await OrderService.UpdateOrderStatusAsync(request);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating order status: {ex.Message}");
        }
    }

    private async Task MarkAsPaid()
    {
        try
        {
            var request = new UpdatePaymentStatusRequest
            {
                OrderId = _currentOrder.Id,
                PaymentStatus = "Payé"
            };

            var success = await OrderService.UpdatePaymentStatusAsync(request);
            if (success)
            {
                _currentOrder.PaymentStatus = "Payé";
                StateHasChanged();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error updating payment status: {ex.Message}");
        }
    }

    private async Task SaveNotes()
    {
        try
        {
            var updateRequest = new UpdateOrderStatusRequest
            {
                OrderId = _currentOrder.Id,
                Status = _currentOrder.Status,
                Notes = _currentOrder.Notes
            };
            var success = await OrderService.UpdateOrderStatusAsync(updateRequest);
            if (success)
            {
                Console.WriteLine("Notes saved successfully");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error saving notes: {ex.Message}");
        }
    }

    private async Task LoadSellerInfo()
    {
        try
        {
            var currentUser = await AuthService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                // Récupérer l'ID du vendeur basé sur l'ID utilisateur
                var sellerInfo = await ProductService.GetSellerByUserIdAsync(currentUser.Id);
                if (sellerInfo != null)
                {
                    _sellerId = sellerInfo.Id;
                    Console.WriteLine($"Vendeur connecté: ID={_sellerId}, Nom={sellerInfo.Name}");
                }
                else
                {
                    Console.WriteLine($"Aucun vendeur trouvé pour l'utilisateur {currentUser.Id}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la récupération des informations du vendeur: {ex.Message}");
        }
    }
}
