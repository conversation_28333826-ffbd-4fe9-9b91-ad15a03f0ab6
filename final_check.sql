-- Vérification finale de la répartition des commandes par vendeur
SELECT
    oi."SellerId",
    COUNT(DISTINCT o."Id") as "NombreCommandes",
    SUM(o."TotalAmount") as "MontantTotal"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
GROUP BY oi."SellerId"
ORDER BY oi."SellerId";

-- Détail des commandes par vendeur
SELECT
    oi."SellerId",
    o."Id" as "OrderId",
    o."UserId" as "CustomerUserId",
    o."Status",
    o."TotalAmount"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
WHERE oi."SellerId" IN (7, 8)
ORDER BY oi."SellerId", o."Id";
