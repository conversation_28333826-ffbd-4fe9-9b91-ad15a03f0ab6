using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.SellerPortal.Models.Reviews;
using Blazored.LocalStorage;
using System.Net.Http.Headers;
using System.IdentityModel.Tokens.Jwt;

namespace NafaPlace.SellerPortal.Services;

public class ReviewService : IReviewService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly JsonSerializerOptions _jsonOptions;

    public ReviewService(IHttpClientFactory httpClientFactory, ILocalStorageService localStorage)
    {
        _httpClient = httpClientFactory.CreateClient("ReviewApi");
        _localStorage = localStorage;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<ReviewsPagedResponse> GetSellerReviewsAsync(ReviewFilterRequest request)
    {
        try
        {
            await SetAuthorizationHeader();

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new ReviewsPagedResponse
                {
                    Reviews = new List<ReviewDto>(),
                    TotalCount = 0,
                    TotalPages = 0,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize
                };
            }

            var queryParams = new List<string>();
            if (request.ProductId.HasValue)
                queryParams.Add($"productId={request.ProductId}");
            if (request.Rating.HasValue)
                queryParams.Add($"rating={request.Rating}");
            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={request.Status}");
            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            queryParams.Add($"page={request.Page}");
            queryParams.Add($"pageSize={request.PageSize}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"/api/reviews/seller/{sellerId}?{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewsPagedResponse>(content, _jsonOptions) ?? new ReviewsPagedResponse();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des avis vendeur: {ex.Message}");
        }

        // Retourner une réponse vide au lieu des données de démonstration
        return new ReviewsPagedResponse
        {
            Reviews = new List<ReviewDto>(),
            TotalCount = 0,
            TotalPages = 0,
            CurrentPage = request.Page,
            PageSize = request.PageSize
        };
    }

    public async Task<ReviewStatsDto> GetSellerReviewStatsAsync()
    {
        try
        {
            await SetAuthorizationHeader();

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new ReviewStatsDto();
            }

            var response = await _httpClient.GetAsync($"/api/reviews/seller/{sellerId}/stats");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewStatsDto>(content, _jsonOptions) ?? new ReviewStatsDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques: {ex.Message}");
        }

        // Retourner des statistiques vides au lieu des données de démonstration
        return new ReviewStatsDto
        {
            TotalReviews = 0,
            AverageRating = 0,
            RecentReviews = 0,
            PendingReviews = 0,
            RatingDistribution = new Dictionary<int, int>
            {
                { 5, 0 },
                { 4, 0 },
                { 3, 0 },
                { 2, 0 },
                { 1, 0 }
            }
        };
    }

    public async Task<List<ProductSummaryDto>> GetSellerProductsAsync()
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync("/api/products/seller/summary");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ProductSummaryDto>>(content, _jsonOptions) ?? new List<ProductSummaryDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des produits: {ex.Message}");
        }

        // Retourner une liste vide au lieu des données de démonstration
        return new List<ProductSummaryDto>();
    }

    public async Task<ReviewDto?> GetReviewByIdAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/{reviewId}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewDto>(content, _jsonOptions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de l'avis: {ex.Message}");
        }

        return null;
    }

    public async Task<bool> ReplyToReviewAsync(CreateReviewReplyRequest request)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/reply", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la rÃ©ponse Ã  l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<List<ReviewReplyDto>> GetReviewRepliesAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/{reviewId}/replies");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ReviewReplyDto>>(content, _jsonOptions) ?? new List<ReviewReplyDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des rÃ©ponses: {ex.Message}");
        }

        return new List<ReviewReplyDto>();
    }

    private async Task SetAuthorizationHeader()
    {
        var token = await _localStorage.GetItemAsync<string>("authToken");
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }
    }

    private ReviewsPagedResponse GetDemoReviewsData(ReviewFilterRequest request)
    {
        var allReviews = new List<ReviewDto>
        {
            new() { Id = 1, ProductId = 1, ProductName = "Smartphone Samsung Galaxy", ProductImageUrl = "/images/products/samsung.jpg", 
                   UserId = "user1", UserName = "Mamadou Diallo", Rating = 5, Title = "Excellent produit", 
                   Comment = "TrÃ¨s satisfait de cet achat, le tÃ©lÃ©phone fonctionne parfaitement.", IsApproved = true, 
                   IsVerifiedPurchase = true, HelpfulCount = 3, CreatedAt = DateTime.Now.AddDays(-2) },
            
            new() { Id = 2, ProductId = 1, ProductName = "Smartphone Samsung Galaxy", ProductImageUrl = "/images/products/samsung.jpg", 
                   UserId = "user2", UserName = "Fatoumata Camara", Rating = 4, Title = "Bon rapport qualitÃ©-prix", 
                   Comment = "Le produit correspond Ã  mes attentes, livraison rapide.", IsApproved = true, 
                   IsVerifiedPurchase = true, HelpfulCount = 1, CreatedAt = DateTime.Now.AddDays(-5) },
            
            new() { Id = 3, ProductId = 2, ProductName = "Ordinateur Portable HP", ProductImageUrl = "/images/products/hp-laptop.jpg", 
                   UserId = "user3", UserName = "Ibrahima Sow", Rating = 3, Title = "Correct mais peut mieux faire", 
                   Comment = "L'ordinateur fonctionne bien mais la batterie ne tient pas longtemps.", IsApproved = null, 
                   IsVerifiedPurchase = true, HelpfulCount = 0, CreatedAt = DateTime.Now.AddDays(-1) },
            
            new() { Id = 4, ProductId = 3, ProductName = "Casque Audio Sony", ProductImageUrl = "/images/products/sony-headphones.jpg", 
                   UserId = "user4", UserName = "Aminata TourÃ©", Rating = 5, Title = "Son exceptionnel", 
                   Comment = "La qualitÃ© audio est remarquable, trÃ¨s confortable Ã  porter.", IsApproved = true, 
                   IsVerifiedPurchase = false, HelpfulCount = 5, CreatedAt = DateTime.Now.AddDays(-7) },
            
            new() { Id = 5, ProductId = 4, ProductName = "Tablette iPad Air", ProductImageUrl = "/images/products/ipad.jpg", 
                   UserId = "user5", UserName = "Ousmane Barry", Rating = 4, Title = "TrÃ¨s bonne tablette", 
                   Comment = "Interface fluide et Ã©cran de qualitÃ©, parfait pour le travail.", IsApproved = true, 
                   IsVerifiedPurchase = true, HelpfulCount = 2, CreatedAt = DateTime.Now.AddDays(-10) }
        };

        // Apply filters
        var filteredReviews = allReviews.AsQueryable();

        if (request.ProductId.HasValue && request.ProductId > 0)
            filteredReviews = filteredReviews.Where(r => r.ProductId == request.ProductId);

        if (request.Rating.HasValue && request.Rating > 0)
            filteredReviews = filteredReviews.Where(r => r.Rating == request.Rating);

        if (!string.IsNullOrEmpty(request.Status))
        {
            filteredReviews = request.Status switch
            {
                "approved" => filteredReviews.Where(r => r.IsApproved == true),
                "pending" => filteredReviews.Where(r => r.IsApproved == null),
                "rejected" => filteredReviews.Where(r => r.IsApproved == false),
                _ => filteredReviews
            };
        }

        if (!string.IsNullOrEmpty(request.SearchTerm))
        {
            var searchTerm = request.SearchTerm.ToLower();
            filteredReviews = filteredReviews.Where(r => 
                r.ProductName.ToLower().Contains(searchTerm) ||
                r.UserName.ToLower().Contains(searchTerm) ||
                r.Title.ToLower().Contains(searchTerm) ||
                r.Comment.ToLower().Contains(searchTerm));
        }

        var totalCount = filteredReviews.Count();
        var totalPages = (int)Math.Ceiling((double)totalCount / request.PageSize);
        
        var pagedReviews = filteredReviews
            .OrderByDescending(r => r.CreatedAt)
            .Skip((request.Page - 1) * request.PageSize)
            .Take(request.PageSize)
            .ToList();

        return new ReviewsPagedResponse
        {
            Reviews = pagedReviews,
            TotalCount = totalCount,
            TotalPages = totalPages,
            CurrentPage = request.Page,
            PageSize = request.PageSize
        };
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
                return 0;

            // Décoder le JWT pour récupérer le SellerId
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            var sellerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "SellerId" || x.Type == "sellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                return sellerId;
            }

            // Si pas de SellerId dans le token, utiliser une valeur par défaut pour les tests
            return 1;
        }
        catch (Exception)
        {
            return 1; // Valeur par défaut pour les tests
        }
    }
}

