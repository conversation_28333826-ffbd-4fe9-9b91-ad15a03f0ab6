using System.Net.Http.Json;
using System.Text.Json;
using NafaPlace.SellerPortal.Models.Reviews;
using Blazored.LocalStorage;
using System.Net.Http.Headers;
using System.IdentityModel.Tokens.Jwt;

namespace NafaPlace.SellerPortal.Services;

public class ReviewService : IReviewService
{
    private readonly HttpClient _httpClient;
    private readonly ILocalStorageService _localStorage;
    private readonly JsonSerializerOptions _jsonOptions;

    public ReviewService(IHttpClientFactory httpClientFactory, ILocalStorageService localStorage)
    {
        _httpClient = httpClientFactory.CreateClient("ReviewApi");
        _localStorage = localStorage;
        _jsonOptions = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
    }

    public async Task<ReviewsPagedResponse> GetSellerReviewsAsync(ReviewFilterRequest request)
    {
        try
        {
            await SetAuthorizationHeader();

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new ReviewsPagedResponse
                {
                    Reviews = new List<ReviewDto>(),
                    TotalCount = 0,
                    TotalPages = 0,
                    CurrentPage = request.Page,
                    PageSize = request.PageSize
                };
            }

            var queryParams = new List<string>();
            if (request.ProductId.HasValue)
                queryParams.Add($"productId={request.ProductId}");
            if (request.Rating.HasValue)
                queryParams.Add($"rating={request.Rating}");
            if (!string.IsNullOrEmpty(request.Status))
                queryParams.Add($"status={request.Status}");
            if (!string.IsNullOrEmpty(request.SearchTerm))
                queryParams.Add($"searchTerm={Uri.EscapeDataString(request.SearchTerm)}");
            queryParams.Add($"page={request.Page}");
            queryParams.Add($"pageSize={request.PageSize}");

            var queryString = string.Join("&", queryParams);
            var response = await _httpClient.GetAsync($"/api/reviews/seller/{sellerId}?{queryString}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewsPagedResponse>(content, _jsonOptions) ?? new ReviewsPagedResponse();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des avis vendeur: {ex.Message}");
        }

        // Retourner une réponse vide au lieu des données de démonstration
        return new ReviewsPagedResponse
        {
            Reviews = new List<ReviewDto>(),
            TotalCount = 0,
            TotalPages = 0,
            CurrentPage = request.Page,
            PageSize = request.PageSize
        };
    }

    public async Task<ReviewStatsDto> GetSellerReviewStatsAsync()
    {
        try
        {
            await SetAuthorizationHeader();

            // Récupérer l'ID du vendeur connecté
            var sellerId = await GetCurrentSellerIdAsync();
            if (sellerId == 0)
            {
                return new ReviewStatsDto();
            }

            var response = await _httpClient.GetAsync($"/api/reviews/seller/{sellerId}/stats");

            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewStatsDto>(content, _jsonOptions) ?? new ReviewStatsDto();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des statistiques: {ex.Message}");
        }

        // Retourner des statistiques vides au lieu des données de démonstration
        return new ReviewStatsDto
        {
            TotalReviews = 0,
            AverageRating = 0,
            RecentReviews = 0,
            PendingReviews = 0,
            RatingDistribution = new Dictionary<int, int>
            {
                { 5, 0 },
                { 4, 0 },
                { 3, 0 },
                { 2, 0 },
                { 1, 0 }
            }
        };
    }

    public async Task<List<ProductSummaryDto>> GetSellerProductsAsync()
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync("/api/products/seller/summary");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ProductSummaryDto>>(content, _jsonOptions) ?? new List<ProductSummaryDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des produits: {ex.Message}");
        }

        // Retourner une liste vide au lieu des données de démonstration
        return new List<ProductSummaryDto>();
    }

    public async Task<ReviewDto?> GetReviewByIdAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/{reviewId}");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<ReviewDto>(content, _jsonOptions);
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement de l'avis: {ex.Message}");
        }

        return null;
    }

    public async Task<bool> ReplyToReviewAsync(CreateReviewReplyRequest request)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.PostAsJsonAsync("/api/reviews/reply", request);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la réponse à l'avis: {ex.Message}");
            return false;
        }
    }

    public async Task<List<ReviewReplyDto>> GetReviewRepliesAsync(int reviewId)
    {
        try
        {
            await SetAuthorizationHeader();
            var response = await _httpClient.GetAsync($"/api/reviews/{reviewId}/replies");
            
            if (response.IsSuccessStatusCode)
            {
                var content = await response.Content.ReadAsStringAsync();
                return JsonSerializer.Deserialize<List<ReviewReplyDto>>(content, _jsonOptions) ?? new List<ReviewReplyDto>();
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des réponses: {ex.Message}");
        }

        return new List<ReviewReplyDto>();
    }

    private async Task SetAuthorizationHeader()
    {
        var token = await _localStorage.GetItemAsync<string>("authToken");
        if (!string.IsNullOrEmpty(token))
        {
            _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Bearer", token);
        }
    }

    private async Task<int> GetCurrentSellerIdAsync()
    {
        try
        {
            var token = await _localStorage.GetItemAsync<string>("authToken");
            if (string.IsNullOrEmpty(token))
                return 0;

            // Décoder le JWT pour récupérer le SellerId
            var handler = new System.IdentityModel.Tokens.Jwt.JwtSecurityTokenHandler();
            var jsonToken = handler.ReadJwtToken(token);

            var sellerIdClaim = jsonToken.Claims.FirstOrDefault(x => x.Type == "SellerId" || x.Type == "sellerId");
            if (sellerIdClaim != null && int.TryParse(sellerIdClaim.Value, out int sellerId))
            {
                return sellerId;
            }

            // Si pas de SellerId dans le token, utiliser une valeur par défaut pour les tests
            return 1;
        }
        catch (Exception)
        {
            return 1; // Valeur par défaut pour les tests
        }
    }
}
