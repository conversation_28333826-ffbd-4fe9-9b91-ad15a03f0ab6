@page "/dashboard"
@page "/"
@attribute [Authorize]
@using NafaPlace.SellerPortal.Models.Auth
@using NafaPlace.SellerPortal.Models.Orders
@using NafaPlace.SellerPortal.Models.Statistics
@using NafaPlace.SellerPortal.Services
@inject IAuthService AuthService
@inject IOrderService OrderService
@inject IStatisticsService StatisticsService
@inject NotificationService NotificationService
@inject ProductService ProductService

<PageTitle>Dashboard Vendeur - NafaPlace</PageTitle>

<div class="container-fluid px-4">
    <h1 class="mt-4">Dashboard Vendeur</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item active">Dashboard</li>
    </ol>
    <div class="row">
        <div class="col-xl-3 col-md-6">
            <div class="card bg-primary text-white mb-4">
                <div class="card-body">
                    <h4>Produits</h4>
                    <h2>@_productCount</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/products">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-warning text-white mb-4">
                <div class="card-body">
                    <h4>Commandes en attente</h4>
                    <h2>@_pendingOrdersCount</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/orders">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-success text-white mb-4">
                <div class="card-body">
                    <h4>Ventes (mois)</h4>
                    <h2>@_monthlySales.ToString("N0") GNF</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/statistics">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="card bg-danger text-white mb-4">
                <div class="card-body">
                    <h4>Produits en rupture</h4>
                    <h2>@_outOfStockCount</h2>
                </div>
                <div class="card-footer d-flex align-items-center justify-content-between">
                    <a class="small text-white stretched-link" href="/products/out-of-stock">Voir les détails</a>
                    <div class="small text-white"><i class="bi bi-arrow-right"></i></div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-graph-up me-1"></i>
                    Ventes mensuelles
                </div>
                <div class="card-body">
                    <!-- Placeholder pour le graphique des ventes -->
                    <div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                        <p class="text-muted">Graphique des ventes mensuelles</p>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="bi bi-bar-chart me-1"></i>
                    Produits les plus vendus
                </div>
                <div class="card-body">
                    <!-- Placeholder pour le graphique des produits les plus vendus -->
                    <div style="height: 300px; background-color: #f8f9fa; display: flex; align-items: center; justify-content: center;">
                        <p class="text-muted">Graphique des produits les plus vendus</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="card mb-4">
        <div class="card-header">
            <i class="bi bi-table me-1"></i>
            Commandes récentes
        </div>
        <div class="card-body">
            @if (_isLoading)
            {
                <div class="d-flex justify-content-center my-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else
            {
                <table class="table table-bordered table-hover">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Client</th>
                            <th>Date</th>
                            <th>Montant</th>
                            <th>Statut</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var order in _recentOrders)
                        {
                            <tr>
                                <td>@order.Id</td>
                                <td>@order.CustomerName</td>
                                <td>@order.OrderDate.ToShortDateString()</td>
                                <td>@order.TotalAmount GNF</td>
                                <td>
                                    <span class="badge @GetStatusBadgeClass(order.Status)">
                                        @order.Status
                                    </span>
                                </td>
                                <td>
                                    <a href="/orders/@order.Id" class="btn btn-sm btn-primary">
                                        <i class="bi bi-eye"></i>
                                    </a>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            }
        </div>
    </div>
</div>

@code {
    private bool _isLoading = false;
    private UserDto? _currentUser;
    private int _productCount = 0;
    private int _pendingOrdersCount = 0;
    private decimal _monthlySales = 0;
    private int _outOfStockCount = 0;
    private List<OrderSummary> _recentOrders = new List<OrderSummary>();

    protected override async Task OnInitializedAsync()
    {
        try
        {
            _isLoading = true;

            // Récupérer les informations de l'utilisateur connecté
            _currentUser = await AuthService.GetCurrentUserAsync();

            // Charger les vraies données du dashboard
            await LoadDashboardDataAsync();
        }
        catch (Exception ex)
        {
            NotificationService.ShowError($"Erreur lors du chargement des données: {ex.Message}");
        }
        finally
        {
            _isLoading = false;
        }
    }

    private async Task LoadDashboardDataAsync()
    {
        try
        {
            // Charger les statistiques du dashboard
            var statsRequest = new StatisticsRequest
            {
                StartDate = DateTime.Now.AddDays(-30),
                EndDate = DateTime.Now
            };

            var dashboardStats = await StatisticsService.GetDashboardStatisticsAsync(statsRequest);

            if (dashboardStats?.SalesStats != null)
            {
                _monthlySales = (decimal)dashboardStats.SalesStats.TotalSales;
                _pendingOrdersCount = dashboardStats.SalesStats.TotalOrders;
            }

            // Charger les commandes récentes
            var ordersRequest = new OrderFilterRequest
            {
                PageNumber = 1,
                PageSize = 5,
                SortBy = "OrderDate",
                SortDirection = "desc"
            };

            var ordersResponse = await OrderService.GetOrdersAsync(ordersRequest);

            if (ordersResponse?.Orders != null)
            {
                _recentOrders = ordersResponse.Orders.Select(o => new OrderSummary
                {
                    Id = o.OrderNumber,
                    CustomerName = o.CustomerName,
                    OrderDate = o.OrderDate,
                    TotalAmount = o.TotalAmount,
                    Status = o.Status
                }).ToList();

                // Compter les commandes en attente
                _pendingOrdersCount = ordersResponse.Orders.Count(o => o.Status == "En attente" || o.Status == "Pending");
            }

            // Récupérer le nombre de produits du vendeur
            var currentUser = await AuthService.GetCurrentUserAsync();
            if (currentUser != null)
            {
                var sellerInfo = await ProductService.GetSellerByUserIdAsync(currentUser.Id);
                if (sellerInfo != null)
                {
                    _productCount = await ProductService.GetProductsCountAsync(sellerInfo.Id);
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors du chargement des données du dashboard: {ex.Message}");
            // En cas d'erreur, garder les valeurs par défaut (0)
        }
    }

    private string GetStatusBadgeClass(string status)
    {
        return status switch
        {
            "En attente" => "bg-warning",
            "Expédié" => "bg-info",
            "Livré" => "bg-success",
            "Annulé" => "bg-danger",
            _ => "bg-secondary"
        };
    }

    public class OrderSummary
    {
        public string Id { get; set; } = string.Empty;
        public string CustomerName { get; set; } = string.Empty;
        public DateTime OrderDate { get; set; } = DateTime.Now;
        public decimal TotalAmount { get; set; }
        public string Status { get; set; } = string.Empty;
    }
}
