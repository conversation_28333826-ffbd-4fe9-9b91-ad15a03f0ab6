using Microsoft.AspNetCore.Mvc;
using NafaPlace.Order.Application;
using NafaPlace.Order.API.DTOs;

namespace NafaPlace.Order.API.Controllers;

[ApiController]
[Route("api/[controller]")]
public class StatisticsController : ControllerBase
{
    private readonly IOrderRepository _repository;

    public StatisticsController(IOrderRepository repository)
    {
        _repository = repository;
    }

    [HttpGet("seller/{sellerId}")]
    public async Task<ActionResult<SellerStatisticsDto>> GetSellerStatistics(int sellerId)
    {
        var orders = await _repository.GetOrdersBySellerIdAsync(sellerId);
        var totalOrders = await _repository.GetOrdersCountBySellerIdAsync(sellerId);
        
        var statistics = new SellerStatisticsDto
        {
            TotalOrders = totalOrders,
            TotalRevenue = orders.SelectMany(o => o.OrderItems.Where(oi => oi.SellerId == sellerId))
                                .Sum(oi => oi.UnitPrice * oi.Quantity),
            PendingOrders = orders.Count(o => o.Status == Domain.OrderStatus.Pending),
            CompletedOrders = orders.Count(o => o.Status == Domain.OrderStatus.Completed),
            CancelledOrders = orders.Count(o => o.Status == Domain.OrderStatus.Cancelled),
            AverageOrderValue = totalOrders > 0 ? 
                orders.SelectMany(o => o.OrderItems.Where(oi => oi.SellerId == sellerId))
                      .Sum(oi => oi.UnitPrice * oi.Quantity) / totalOrders : 0,
            RecentOrdersCount = orders.Count(o => o.OrderDate >= DateTime.UtcNow.AddDays(-30)),
            TopSellingProducts = GetTopSellingProducts(orders, sellerId)
        };

        return Ok(statistics);
    }

    [HttpGet("seller/{sellerId}/revenue")]
    public async Task<ActionResult<RevenueStatisticsDto>> GetSellerRevenueStatistics(
        int sellerId,
        [FromQuery] DateTime? startDate = null,
        [FromQuery] DateTime? endDate = null)
    {
        startDate ??= DateTime.UtcNow.AddMonths(-12);
        endDate ??= DateTime.UtcNow;

        var orders = await _repository.GetOrdersBySellerIdAsync(sellerId);
        var filteredOrders = orders.Where(o => o.OrderDate >= startDate && o.OrderDate <= endDate);

        var monthlyRevenue = filteredOrders
            .GroupBy(o => new { o.OrderDate.Year, o.OrderDate.Month })
            .Select(g => new MonthlyRevenueDto
            {
                Year = g.Key.Year,
                Month = g.Key.Month,
                Revenue = g.SelectMany(o => o.OrderItems.Where(oi => oi.SellerId == sellerId))
                          .Sum(oi => oi.UnitPrice * oi.Quantity),
                OrderCount = g.Count()
            })
            .OrderBy(m => m.Year)
            .ThenBy(m => m.Month)
            .ToList();

        var revenueStats = new RevenueStatisticsDto
        {
            TotalRevenue = monthlyRevenue.Sum(m => m.Revenue),
            MonthlyRevenue = monthlyRevenue,
            AverageMonthlyRevenue = monthlyRevenue.Any() ? monthlyRevenue.Average(m => m.Revenue) : 0,
            BestMonth = monthlyRevenue.OrderByDescending(m => m.Revenue).FirstOrDefault(),
            GrowthRate = CalculateGrowthRate(monthlyRevenue)
        };

        return Ok(revenueStats);
    }

    private List<TopProductDto> GetTopSellingProducts(IEnumerable<Domain.Order> orders, int sellerId)
    {
        return orders
            .SelectMany(o => o.OrderItems.Where(oi => oi.SellerId == sellerId))
            .GroupBy(oi => new { oi.ProductId, oi.ProductName })
            .Select(g => new TopProductDto
            {
                ProductId = g.Key.ProductId,
                ProductName = g.Key.ProductName,
                TotalQuantitySold = g.Sum(oi => oi.Quantity),
                TotalRevenue = g.Sum(oi => oi.UnitPrice * oi.Quantity)
            })
            .OrderByDescending(p => p.TotalQuantitySold)
            .Take(10)
            .ToList();
    }

    private decimal CalculateGrowthRate(List<MonthlyRevenueDto> monthlyRevenue)
    {
        if (monthlyRevenue.Count < 2) return 0;

        var lastMonth = monthlyRevenue.LastOrDefault();
        var previousMonth = monthlyRevenue.Skip(monthlyRevenue.Count - 2).FirstOrDefault();

        if (previousMonth?.Revenue == 0) return 0;

        return ((lastMonth?.Revenue ?? 0) - (previousMonth?.Revenue ?? 0)) / (previousMonth?.Revenue ?? 1) * 100;
    }
}
