{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "ConnectionStrings": {"DefaultConnection": "Server=localhost;Port=5434;Database=NafaPlace.Order;User Id=postgres;Password=*****************"}, "AllowedHosts": "*", "ServiceUrls": {"CartApi": "http://cart-api", "CatalogApi": "http://catalog-api"}, "JwtSettings": {"SecretKey": "NafaPlace2025@SecretKeyForJWTTokenGeneration!", "Issuer": "NafaPlace", "Audience": "NafaPlace-Users", "ExpirationInMinutes": 60}}