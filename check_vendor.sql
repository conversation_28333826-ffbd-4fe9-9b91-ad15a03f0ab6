-- Corri<PERSON> le SellerId de la commande existante (ProductId = 19 -> SellerId = 13)
UPDATE "OrderItems"
SET "SellerId" = 13
WHERE "ProductId" = 19 AND "SellerId" = 1;

-- Vérifier la correction
SELECT o."Id", o."UserId", o."OrderDate", o."TotalAmount", o."Status",
       oi."ProductId", oi."SellerId", oi."Quantity", oi."UnitPrice"
FROM "Orders" o
JOIN "OrderItems" oi ON o."Id" = oi."OrderId"
WHERE o."Id" = 30;
