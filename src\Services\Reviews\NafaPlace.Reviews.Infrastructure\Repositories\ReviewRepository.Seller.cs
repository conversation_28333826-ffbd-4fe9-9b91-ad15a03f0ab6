using Microsoft.EntityFrameworkCore;
using NafaPlace.Reviews.Domain.Models;
using NafaPlace.Reviews.Infrastructure.Data;

namespace NafaPlace.Reviews.Infrastructure.Repositories;

public partial class ReviewRepository : IReviewRepository
{
    // Seller-specific operations
    
    public async Task<IEnumerable<Review>> GetReviewsBySellerIdAsync(int sellerId, int page = 1, int pageSize = 10)
    {
        // Pour récupérer les avis d'un vendeur, nous devons joindre avec les produits
        // Nous utiliserons un appel HTTP vers l'API Catalog pour récupérer les produits du vendeur
        var productIds = await GetSellerProductIdsAsync(sellerId);
        
        if (!productIds.Any())
        {
            return new List<Review>();
        }
        
        return await _context.Reviews
            .Where(r => productIds.Contains(r.ProductId))
            .Include(r => r.ReviewHelpfuls)
            .Include(r => r.Replies)
            .OrderByDescending(r => r.CreatedAt)
            .Skip((page - 1) * pageSize)
            .Take(pageSize)
            .ToListAsync();
    }
    
    public async Task<int> GetTotalReviewsCountBySellerIdAsync(int sellerId)
    {
        var productIds = await GetSellerProductIdsAsync(sellerId);
        
        if (!productIds.Any())
        {
            return 0;
        }
        
        return await _context.Reviews
            .Where(r => productIds.Contains(r.ProductId))
            .CountAsync();
    }
    
    public async Task<double> GetAverageRatingBySellerIdAsync(int sellerId)
    {
        var productIds = await GetSellerProductIdsAsync(sellerId);
        
        if (!productIds.Any())
        {
            return 0;
        }
        
        var reviews = await _context.Reviews
            .Where(r => productIds.Contains(r.ProductId))
            .ToListAsync();
            
        return reviews.Any() ? reviews.Average(r => r.Rating) : 0;
    }
    
    public async Task<Dictionary<int, int>> GetRatingDistributionBySellerIdAsync(int sellerId)
    {
        var productIds = await GetSellerProductIdsAsync(sellerId);
        
        if (!productIds.Any())
        {
            return new Dictionary<int, int> { { 1, 0 }, { 2, 0 }, { 3, 0 }, { 4, 0 }, { 5, 0 } };
        }
        
        var distribution = await _context.Reviews
            .Where(r => productIds.Contains(r.ProductId))
            .GroupBy(r => r.Rating)
            .Select(g => new { Rating = g.Key, Count = g.Count() })
            .ToDictionaryAsync(x => x.Rating, x => x.Count);
            
        // Ensure all ratings 1-5 are present
        for (int i = 1; i <= 5; i++)
        {
            if (!distribution.ContainsKey(i))
            {
                distribution[i] = 0;
            }
        }
        
        return distribution;
    }
    
    public async Task<int> GetRecentReviewsCountBySellerIdAsync(int sellerId, int days = 30)
    {
        var productIds = await GetSellerProductIdsAsync(sellerId);
        
        if (!productIds.Any())
        {
            return 0;
        }
        
        var cutoffDate = DateTime.UtcNow.AddDays(-days);
        
        return await _context.Reviews
            .Where(r => productIds.Contains(r.ProductId) && r.CreatedAt >= cutoffDate)
            .CountAsync();
    }
    
    public async Task<int> GetPendingReviewsCountBySellerIdAsync(int sellerId)
    {
        var productIds = await GetSellerProductIdsAsync(sellerId);
        
        if (!productIds.Any())
        {
            return 0;
        }
        
        return await _context.Reviews
            .Where(r => productIds.Contains(r.ProductId) && r.Status == ReviewStatus.Pending)
            .CountAsync();
    }
    
    private async Task<List<int>> GetSellerProductIdsAsync(int sellerId)
    {
        // TODO: Implémenter l'appel vers l'API Catalog pour récupérer les produits du vendeur
        // Pour l'instant, retournons une liste vide
        // Cette méthode devra être implémentée avec un HttpClient vers l'API Catalog
        
        try
        {
            // Simuler un appel API - à remplacer par un vrai appel HTTP
            // var catalogApiUrl = _configuration["ServiceUrls:CatalogApi"];
            // var response = await _httpClient.GetAsync($"{catalogApiUrl}/api/products/seller/{sellerId}");
            // ...
            
            return new List<int>(); // Temporaire
        }
        catch
        {
            return new List<int>();
        }
    }
}
