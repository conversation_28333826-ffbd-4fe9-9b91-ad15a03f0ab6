using Microsoft.EntityFrameworkCore;
using NafaPlace.Order.Application;
using NafaPlace.Order.Domain;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace NafaPlace.Order.Infrastructure
{
    public class OrderRepository : IOrderRepository
    {
        private readonly OrderDbContext _context;

        public OrderRepository(OrderDbContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Domain.Order>> GetAllOrdersAsync()
        {
            return await _context.Orders
                .Include(o => o.OrderItems)
                .OrderByDescending(o => o.OrderDate)
                .ToListAsync();
        }

        public async Task<Domain.Order> GetOrderByIdAsync(int id)
        {
            return await _context.Orders.Include(o => o.OrderItems).FirstOrDefaultAsync(o => o.Id == id) ?? new Domain.Order();
        }

        public async Task<IEnumerable<Domain.Order>> GetOrdersByUserIdAsync(string userId)
        {
            return await _context.Orders.Include(o => o.OrderItems).Where(o => o.UserId == userId).ToListAsync();
        }

        public async Task<Domain.Order> CreateOrderAsync(Domain.Order order)
        {
            _context.Orders.Add(order);
            await _context.SaveChangesAsync();
            return order;
        }

        public async Task<Domain.Order> UpdateOrderAsync(Domain.Order order)
        {
            _context.Entry(order).State = EntityState.Modified;
            await _context.SaveChangesAsync();
            return order;
        }

        public async Task<IEnumerable<Domain.Order>> GetOrdersWithFiltersAsync(
            string? searchTerm = null,
            string? status = null,
            string? paymentStatus = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageNumber = 1,
            int pageSize = 20)
        {
            var query = _context.Orders
                .Include(o => o.OrderItems)
                .AsQueryable();

            // Appliquer les filtres
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(o =>
                    o.Id.ToString().Contains(searchTerm) ||
                    o.UserId.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(o => o.Status.ToString() == status);
            }

            if (!string.IsNullOrEmpty(paymentStatus))
            {
                query = query.Where(o => o.PaymentStatus.ToString() == paymentStatus);
            }

            if (startDate.HasValue)
            {
                query = query.Where(o => o.OrderDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(o => o.OrderDate <= endDate.Value.AddDays(1));
            }

            // Appliquer la pagination et l'ordre
            return await query
                .OrderByDescending(o => o.OrderDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<IEnumerable<Domain.Order>> GetOrdersBySellerIdAsync(
            int sellerId,
            string? searchTerm = null,
            string? status = null,
            string? paymentStatus = null,
            DateTime? startDate = null,
            DateTime? endDate = null,
            int pageNumber = 1,
            int pageSize = 20)
        {
            var query = _context.Orders
                .Include(o => o.OrderItems)
                .Where(o => o.OrderItems.Any(oi => oi.SellerId == sellerId))
                .AsQueryable();

            // Appliquer les filtres
            if (!string.IsNullOrEmpty(searchTerm))
            {
                query = query.Where(o =>
                    o.Id.ToString().Contains(searchTerm) ||
                    o.UserId.Contains(searchTerm));
            }

            if (!string.IsNullOrEmpty(status))
            {
                query = query.Where(o => o.Status.ToString() == status);
            }

            if (!string.IsNullOrEmpty(paymentStatus))
            {
                query = query.Where(o => o.PaymentStatus.ToString() == paymentStatus);
            }

            if (startDate.HasValue)
            {
                query = query.Where(o => o.OrderDate >= startDate.Value);
            }

            if (endDate.HasValue)
            {
                query = query.Where(o => o.OrderDate <= endDate.Value.AddDays(1));
            }

            // Appliquer la pagination et l'ordre
            return await query
                .OrderByDescending(o => o.OrderDate)
                .Skip((pageNumber - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();
        }

        public async Task<int> GetOrdersCountBySellerIdAsync(int sellerId)
        {
            return await _context.Orders
                .Where(o => o.OrderItems.Any(oi => oi.SellerId == sellerId))
                .CountAsync();
        }
    }
}